### Basilisk Bot: Operational Workflow Analysis

This document outlines the end-to-end operational flow of the Basilisk Bot, from initialization to trade execution, based on a direct analysis of the source code.

#### 1. Initialization and Startup

The bot's lifecycle begins when executed from the command line.

-   **Entry Point (`src/main.rs`):** The `main` function parses command-line arguments. The `run` command is the primary trigger for starting the bot's trading operations.
-   **Operational Mode (`src/operational_modes.rs`):** Based on the specified mode (e.g., `live`, `shadow`), the corresponding function is called. The `run_live_mode` function is the entry point for full production trading. It initializes key components, including the `GazeScanner`, which is responsible for detecting trading opportunities.

#### 2. Step 1: Opportunity Detection (`GazeScanner`)

The `GazeScanner` is the bot's eye on the market, specifically designed to find DEX-DEX arbitrage opportunities.

-   **Scanning Loop (`src/operational_modes.rs`):** Inside `run_live_mode`, a loop continuously calls `gaze_scanner.scan_for_opportunities()`.
-   **Core Scanning Logic (`src/strategies/scanners/gaze.rs`):**
    1.  **Identify Pairs:** The scanner first identifies a list of popular, whitelisted token pairs to analyze.
    2.  **Security Check:** It uses a `HoneypotChecker` to ensure the tokens in a pair are safe to trade.
    3.  **Fetch Prices:** The `get_prices_across_dexes` method queries multiple DEX routers for the price of the token pair. It uses a standard Uniswap V2 ABI and calls the `getAmountsOut` function on each router contract to determine the current exchange rate.
    4.  **Analyze and Identify:** The `analyze_price_differences` method compares the prices from all DEXes. If a price discrepancy is found that exceeds a minimum profit threshold (e.g., > 0.5%), it's flagged as a potential arbitrage opportunity.
    5.  **Create Opportunity:** A `SimpleOpportunity` struct is created, containing all relevant data: the tokens involved, the DEXes for buying and selling, estimated profit, and other metadata.

#### 3. Step 2: The Handoff (NATS Messaging)

Once an opportunity is identified, it must be passed to the execution module. The architecture uses the NATS messaging system for this decoupling.

-   **Publication (Inferred):** While not explicitly shown in `run_live_mode`, the architectural pattern implies that once `GazeScanner` returns a valid opportunity, it is published to a NATS topic, specifically `NatsTopics::EXECUTION_REQUEST`.
-   **Subscription (`src/execution/manager.rs`):** The `ExecutionManager` service runs its own loop (`run` method) which is subscribed to the `NatsTopics::EXECUTION_REQUEST` topic. When a new opportunity is published, the `ExecutionManager` receives it and begins the execution process.

#### 4. Step 3: Trade Execution (`ExecutionManager`)

The `ExecutionManager` is the heart of the execution pipeline, orchestrating a series of validation and safety checks before broadcasting a transaction.

-   **Processing Entry (`process_opportunity`):** This is the main function that handles a received opportunity. The pipeline is as follows:
    1.  **Circuit Breaker Check:** It first consults the `RiskManager` to ensure trading is not globally halted.
    2.  **Nonce Acquisition:** It requests the next valid nonce from the `NonceManager` to prevent transaction replays or conflicts.
    3.  **Transaction Building:** The `build_transaction_for_opportunity` method creates the raw transaction. For a simple DEX arbitrage, this involves creating a standard token transfer. For more complex strategies, it delegates the logic to a `Dispatcher`.
    4.  **Gas Estimation & Bidding:** The `GasEstimator` calculates the required gas, including EIP-1559 parameters. It can apply sophisticated pricing using a `GasStrategy` that considers network conditions and can even engage in "Golden Ratio Bidding" to place competitive bids in MEV auctions.
    5.  **Final Validation:** The `PreExecutionValidator` performs a final check on the fully-formed transaction to catch any last-minute issues.
    6.  **Broadcast:** If all checks pass, the `Broadcaster` signs the transaction with the bot's private key (managed by the `WalletManager`) and sends it to the blockchain via `send_and_confirm`.

#### 5. Step 4: Post-Trade Confirmation

After the transaction is successfully mined and a receipt is received, the `ExecutionManager` performs critical bookkeeping tasks.

-   **Nonce Confirmation:** It informs the `NonceManager` that the nonce has been successfully used, clearing it from the pending state.
-   **PnL Calculation:** It calculates the actual profit or loss by subtracting the true gas cost (from the transaction receipt and a `PriceOracle`) from the initial estimated profit.
-   **Circuit Breaker Update:** It reports the final PnL to the `CircuitBreaker`, which tracks cumulative performance and can halt trading if loss limits are exceeded.
-   **Lifecycle Logging:** A `TradeLifecycleEvent` is published to NATS, logging the final outcome of the trade for monitoring and auditing purposes.

This completes the bot's operational loop, which then repeats continuously.