# Fixed Local Configuration for Zen Geometer
# This configuration matches the expected Config struct format

app_name = "basilisk_bot"
log_level = "info"

[strategy]
kelly_fraction_cap = 0.25
min_profitability_bps = 50
enabled_strategies = ["zen_geometer"]

[execution]
max_slippage_bps = 500
gas_limit_multiplier = 1.2
max_gas_price_gwei = 100
default_slippage_tolerance = "0.005"
max_priority_fee = 2000000000
gas_multiplier_network_shock = 1.5
gas_multiplier_bot_gas_war = 2.0
gas_multiplier_retail_fomo = 1.1
gas_multiplier_high_volatility = 1.05

[secrets]
# Secrets will be loaded from environment variables

[scoring]
quality_ratio_floor = "0.3"
risk_aversion_k = "0.5"
regime_multiplier_retail_fomo = "1.2"
regime_multiplier_high_vol = "0.8"
regime_multiplier_calm = "1.0"
regime_multiplier_gas_war_penalty = "0.5"
temporal_harmonics_weight = "0.33"
geometric_score_weight = "0.33"
network_resonance_weight = "0.34"

[nats]
url = "nats://localhost:4222"
subjects = [
    "market.>",
    "network.blocks",
    "gas.prices",
    "are.temporal_harmonics",
    "are.network_seismology",
    "are.geometric_score",
    "execution.trades",
    "risk.alerts"
]
queue_group = "basilisk_ingestors"

[aetheric_resonance]
base_chain_id = 8453
degen_chain_id = 666666666
min_resonance_score = "0.0"

[chains.8453]
name = "Base"
rpc_url = "https://mainnet.base.org"
max_gas_price = 100000000000
private_key_env_var = "BASILISK_EXECUTION_PRIVATE_KEY"
enabled = true

[chains.8453.contracts]
stargate_compass_v1 = "******************************************"
multicall = "******************************************"

[chains.8453.dex]
uniswap_v2_router = "******************************************"
uniswap_universal_router = "******************************************"

[chains.8453.tokens]
usdc = "******************************************"
weth = "******************************************"