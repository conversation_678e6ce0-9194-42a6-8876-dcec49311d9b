// Debug Kelly Criterion calculations
use rust_decimal::Decimal;
use rust_decimal_macros::dec;

fn main() {
    // Conservative scenario from test
    let total_capital = dec!(10000.0);
    let expected_profit = dec!(100.0);
    let volatility = dec!(0.1);
    let confidence = dec!(0.8);
    let max_position_fraction = dec!(0.25);
    
    // Current implementation calculation
    let profit_margin = expected_profit / total_capital;
    let edge = profit_margin * confidence;
    let variance = volatility * volatility;
    let kelly_fraction = edge / variance;
    let safe_kelly_fraction = kelly_fraction.min(max_position_fraction);
    let position_size = total_capital * safe_kelly_fraction;
    
    println!("=== Kelly Criterion Debug ===");
    println!("Total Capital: ${}", total_capital);
    println!("Expected Profit: ${}", expected_profit);
    println!("Volatility: {}", volatility);
    println!("Confidence: {}", confidence);
    println!("Max Position Fraction: {}", max_position_fraction);
    println!();
    println!("Profit Margin: {}", profit_margin);
    println!("Edge: {}", edge);
    println!("Variance: {}", variance);
    println!("Kelly Fraction: {}", kelly_fraction);
    println!("Safe Kelly Fraction: {}", safe_kelly_fraction);
    println!("Position Size: ${}", position_size);
    println!();
    println!("Expected Position Size: $800.0");
    println!("Actual Position Size: ${}", position_size);
    println!("Deviation: ${}", (position_size - dec!(800.0)).abs());
}